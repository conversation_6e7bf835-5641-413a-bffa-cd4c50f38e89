{"name": "node-setup", "version": "1.0.0", "main": "index.js", "author": "jjvgarcia <<EMAIL>>", "license": "MIT", "scripts": {"build": "babel src --extensions \".js,.ts\" --out-dir dist --copy-files --no-copy-ignored", "dev": "ts-node-dev -r tsconfig-paths/register --respawn --transpile-only --ignore-watch node_modules --no-notify src/server.ts", "start": "node dist/server.js", "test": "jest"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/node": "^7.10.5", "@babel/preset-env": "^7.11.5", "@babel/preset-typescript": "^7.10.4", "@types/express": "^4.17.8", "@types/jest": "^26.0.14", "@types/node": "^14.11.2", "@typescript-eslint/eslint-plugin": "^4.2.0", "babel-plugin-module-resolver": "^4.0.0", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-prettier": "^6.12.0", "eslint-plugin-prettier": "^3.1.4", "jest": "^26.4.2", "prettier": "^2.1.2", "supertest": "^5.0.0", "ts-jest": "^26.4.0", "ts-node": "^9.0.0", "ts-node-dev": "^1.0.0-pre.63", "tsconfig-paths": "^3.9.0", "typescript": "^4.0.3"}, "dependencies": {"@typescript-eslint/parser": "^4.2.0", "express": "^4.17.1"}}