{"compilerOptions": {"target": "es2017", "lib": ["ES6"], "module": "commonjs", "allowJs": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "strict": true, "typeRoots": ["./node_modules/@types", "./src/@types"], "esModuleInterop": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@controllers/*": ["./src/controllers/*"], "@models/*": ["./src/models/*"], "@config/*": ["./src/config/*"]}}, "exclude": ["node_modules", "**/*spec.ts"], "include": ["src/**/*"]}